/*
###
Description:    Batch add properties to download queue with flexible query conditions and multi-board support

Usage:

	# Process all documents without phoLH (single board):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -dryrun"

	# Process multiple boards:
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB,CAR,DDF -dryrun"

	# Process with time range (last 3 months):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -months=3 -dryrun"

	# Process with email statistics report:
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB,CAR -emails=<EMAIL>,<EMAIL> -dryrun"

	# Process specific IDs (bracket format):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -id=[TRBC12248877,TRBC12248878,TRBC12248879] -dryrun"

	# Process documents with custom query (documents that have docLH field and it's not null):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='{\"docLH\":{\"$ne\":null}}' -dryrun"

	# Process documents with simplified query format (minimal quotes):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='{docLH:{$ne:null}}' -dryrun"

	# Process documents with ultra-simplified format (no outer braces needed, but quotes required for shell):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='docLH:{$ne:null}' -dryrun"

	# Process documents with multiple conditions:
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='docLH:{$ne:null},phoLH:{$eq:null}' -dryrun"

	# Complete example with all features:
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB,CAR,DDF -months=6 -emails=<EMAIL>,<EMAIL> -dryrun"

New Features:
- Multi-board processing: Use comma-separated board names (e.g., -board=TRB,CAR,DDF)
- Time range filtering: Use -months=N to process data from last N months
- Media field checking:
  - TRB board: checks 'media' field in merged collection
  - Other boards: checks 'Media' field in corresponding watch table (BoardWatchTable)

- Queue existence checking: Skips items already in download queue
- Email statistics: Send processing statistics to specified email addresses
- Per-board statistics: Tracks processed, added, skipped, and error counts per board

Create date:    2025-07-21
Author:         Maggie
Run frequency:  As needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"strings"
	"time"

	gobase "github.com/real-rm/gobase"
	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
	"github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// BoardStats holds statistics for each board
type BoardStats struct {
	ProcessedCount  int
	AddedCount      int
	SkippedCount    int
	ErrorCount      int
	MediaCheckCount int // 检查media字段的数量
	MediaAddedCount int // 因media字段添加到queue的数量
}

var (
	dryrunFlag      = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	boardFlag       = flag.String("board", "TRB", "Board names for processing (comma-separated, e.g., 'TRB,CAR,DDF')")
	idFlag          = flag.String("id", "", "Property IDs to add to queue (optional, uses priority 50000). Format: id1,id2,id3 or [id1,id2,id3]")
	queryFlag       = flag.String("query", "", "Custom query conditions in JSON format (e.g., 'docLH:{$ne:null}' or '{docLH:{$ne:null}}' or '{\"docLH\":{\"$ne\":null}}') - quotes required to protect shell special characters")
	monthsFlag      = flag.Int("months", 0, "Query data within N months (0 means no time limit)")
	emailsFlag      = flag.String("emails", "", "Email addresses for statistics report (comma-separated)")
	speedMeter      *gospeedmeter.SpeedMeter
	startTime       = time.Now()
	queue           *goresodownload.ResourceDownloadQueue
	boardStatistics = make(map[string]*BoardStats)
)

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

// preprocessQuery converts simplified query format to standard JSON
// Supports: {docLH:{$ne:null}} -> {"docLH":{"$ne":null}}
// Also supports: docLH:{$ne:null} -> {"docLH":{"$ne":null}}
func preprocessQuery(query string) string {
	// If it already looks like proper JSON (contains quotes), return as-is
	if strings.Contains(query, `"`) {
		return query
	}

	// Handle queries without outer braces (e.g., docLH:{$ne:null})
	result := strings.TrimSpace(query)
	if !strings.HasPrefix(result, "{") {
		result = "{" + result + "}"
	}

	// Simple replacements for common patterns
	// Replace common field names
	result = strings.ReplaceAll(result, "docLH:", `"docLH":`)
	result = strings.ReplaceAll(result, "phoLH:", `"phoLH":`)
	result = strings.ReplaceAll(result, "_id:", `"_id":`)

	// Replace MongoDB operators
	result = strings.ReplaceAll(result, "$ne:", `"$ne":`)
	result = strings.ReplaceAll(result, "$eq:", `"$eq":`)
	result = strings.ReplaceAll(result, "$in:", `"$in":`)
	result = strings.ReplaceAll(result, "$size:", `"$size":`)
	result = strings.ReplaceAll(result, "$exists:", `"$exists":`)
	result = strings.ReplaceAll(result, "$gt:", `"$gt":`)
	result = strings.ReplaceAll(result, "$lt:", `"$lt":`)
	result = strings.ReplaceAll(result, "$gte:", `"$gte":`)
	result = strings.ReplaceAll(result, "$lte:", `"$lte":`)
	result = strings.ReplaceAll(result, "$or:", `"$or":`)
	result = strings.ReplaceAll(result, "$and:", `"$and":`)

	// Handle special values - null should remain as null (not quoted)
	// No need to change :null to :"null" because null is a JSON literal

	return result
}

// parseBoards parses comma-separated board names and validates them
func parseBoards(boardStr string) ([]string, error) {
	if boardStr == "" {
		return nil, fmt.Errorf("board parameter cannot be empty")
	}

	boards := strings.Split(boardStr, ",")
	var validBoards []string

	for _, board := range boards {
		board = strings.TrimSpace(board)
		if board == "" {
			continue
		}

		// Validate board
		if _, exists := goresodownload.BoardMergedTable[board]; !exists {
			return nil, fmt.Errorf("invalid board: %s. Valid boards: CAR, DDF, BRE, EDM, TRB", board)
		}

		validBoards = append(validBoards, board)
	}

	if len(validBoards) == 0 {
		return nil, fmt.Errorf("no valid boards found")
	}

	return validBoards, nil
}

// parseEmails parses comma-separated email addresses
func parseEmails(emailStr string) []string {
	if emailStr == "" {
		return nil
	}

	emails := strings.Split(emailStr, ",")
	var validEmails []string

	for _, email := range emails {
		email = strings.TrimSpace(email)
		if email != "" {
			validEmails = append(validEmails, email)
		}
	}

	return validEmails
}

// checkQueueExists checks if a record exists in the download queue
func checkQueueExists(id, board string) (bool, error) {
	ctx := context.Background()
	queueCol := gomongo.Coll("rni", "reso_photo_download_queue")
	if queueCol == nil {
		return false, fmt.Errorf("failed to get queue collection")
	}

	var result bson.M
	err := queueCol.FindOne(ctx, bson.M{"_id": id}).Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// hasMediaField checks if document has media/Media field based on board type
// For TRB: checks 'media' field in the current document (merged collection)
// For other boards: checks 'Media' field in the corresponding watch table
func hasMediaField(doc bson.M, board string) bool {
	if board == "TRB" {
		// For TRB, check 'media' field in current document
		mediaField, exists := doc["media"]
		if !exists || mediaField == nil {
			return false
		}

		// Check if it's a non-empty array
		if mediaArray, ok := mediaField.(primitive.A); ok {
			return len(mediaArray) > 0
		} else if mediaArray, ok := mediaField.([]interface{}); ok {
			return len(mediaArray) > 0
		}

		return false
	} else {
		// For other boards, need to check 'Media' field in watch table
		id, ok := doc["_id"].(string)
		if !ok {
			golog.Error("Failed to extract _id for media check", "board", board)
			return false
		}

		// Get document from watch table
		watchDoc, err := goresodownload.GetNewPropFromWatchTable(id, board)
		if err != nil {
			golog.Debug("Failed to get document from watch table for media check", "_id", id, "board", board, "error", err)
			return false
		}

		mediaField, exists := watchDoc["Media"]
		if !exists || mediaField == nil {
			return false
		}

		// Check if it's a non-empty array
		if mediaArray, ok := mediaField.(primitive.A); ok {
			return len(mediaArray) > 0
		} else if mediaArray, ok := mediaField.([]interface{}); ok {
			return len(mediaArray) > 0
		}

		return false
	}
}

// hasPhoLHField checks if document has phoLH field
func hasPhoLHField(doc bson.M) bool {
	phoLH, exists := doc["phoLH"]
	if !exists || phoLH == nil {
		return false
	}

	// Check if it's a non-empty array
	if phoArray, ok := phoLH.(primitive.A); ok {
		return len(phoArray) > 0
	} else if phoArray, ok := phoLH.([]interface{}); ok {
		return len(phoArray) > 0
	}

	return false
}

// initBoardStats initializes statistics for a board
func initBoardStats(board string) {
	if boardStatistics[board] == nil {
		boardStatistics[board] = &BoardStats{}
	}
}

// sendStatisticsEmail sends statistics report via email
func sendStatisticsEmail(emails []string) error {
	if len(emails) == 0 {
		return nil
	}

	var reportBuilder strings.Builder
	reportBuilder.WriteString("AddToQueue Batch Processing Statistics:\n\n")
	reportBuilder.WriteString(fmt.Sprintf("Processing Time: %v\n", time.Since(startTime)))
	reportBuilder.WriteString(fmt.Sprintf("Dry Run Mode: %v\n\n", *dryrunFlag))

	totalProcessed := 0
	totalAdded := 0
	totalSkipped := 0
	totalErrors := 0
	totalMediaChecked := 0
	totalMediaAdded := 0

	for board, stats := range boardStatistics {
		reportBuilder.WriteString(fmt.Sprintf("Board: %s\n", board))
		reportBuilder.WriteString(fmt.Sprintf("  Processed: %d\n", stats.ProcessedCount))
		reportBuilder.WriteString(fmt.Sprintf("  Added to Queue: %d\n", stats.AddedCount))
		reportBuilder.WriteString(fmt.Sprintf("  Skipped: %d\n", stats.SkippedCount))
		reportBuilder.WriteString(fmt.Sprintf("  Media Checked: %d\n", stats.MediaCheckCount))
		reportBuilder.WriteString(fmt.Sprintf("  Media Added: %d\n", stats.MediaAddedCount))
		reportBuilder.WriteString(fmt.Sprintf("  Errors: %d\n\n", stats.ErrorCount))

		totalProcessed += stats.ProcessedCount
		totalAdded += stats.AddedCount
		totalSkipped += stats.SkippedCount
		totalErrors += stats.ErrorCount
		totalMediaChecked += stats.MediaCheckCount
		totalMediaAdded += stats.MediaAddedCount
	}

	reportBuilder.WriteString("Total Summary:\n")
	reportBuilder.WriteString(fmt.Sprintf("  Total Processed: %d\n", totalProcessed))
	reportBuilder.WriteString(fmt.Sprintf("  Total Added: %d\n", totalAdded))
	reportBuilder.WriteString(fmt.Sprintf("  Total Skipped: %d\n", totalSkipped))
	reportBuilder.WriteString(fmt.Sprintf("  Total Media Checked: %d\n", totalMediaChecked))
	reportBuilder.WriteString(fmt.Sprintf("  Total Media Added: %d\n", totalMediaAdded))
	reportBuilder.WriteString(fmt.Sprintf("  Total Errors: %d\n", totalErrors))

	return gobase.ErrorNotifyEmail(
		reportBuilder.String(),
		emails,
		"addToQueue/main.go",
	)
}

func processProperties(ctx context.Context) error {
	// Parse command line flags
	flag.Parse()
	golog.Info("Starting AddToQueue batch processing", "dryrun", *dryrunFlag, "board", *boardFlag, "id", *idFlag, "query", *queryFlag, "months", *monthsFlag, "emails", *emailsFlag)

	// Parse and validate boards
	boards, err := parseBoards(*boardFlag)
	if err != nil {
		return fmt.Errorf("failed to parse boards: %v", err)
	}

	// Parse emails for statistics report
	emails := parseEmails(*emailsFlag)

	// Initialize ResourceDownloadQueue
	golog.Info("Initializing ResourceDownloadQueue")
	queueCol := gomongo.Coll("rni", "reso_photo_download_queue")
	if queueCol == nil {
		return fmt.Errorf("failed to get queue collection: reso_photo_download_queue")
	}

	var queueErr error
	queue, queueErr = goresodownload.NewResourceDownloadQueue(queueCol)
	if queueErr != nil {
		golog.Error("Failed to initialize ResourceDownloadQueue", "error", queueErr)
		return fmt.Errorf("failed to initialize ResourceDownloadQueue: %v", queueErr)
	}
	golog.Info("ResourceDownloadQueue initialized successfully")

	// Process each board
	for _, board := range boards {
		golog.Info("Processing board", "board", board)

		// Initialize statistics for this board
		initBoardStats(board)

		// Get collection using BoardMergedTable
		collectionName := goresodownload.BoardMergedTable[board]
		coll := gomongo.Coll("rni", collectionName)
		if coll == nil {
			return fmt.Errorf("failed to get merged collection: %s for board %s", collectionName, board)
		}
		golog.Info("Processing collection", "board", board, "collection", collectionName, "dryrun", *dryrunFlag)

		// Process this board
		if processErr := processSingleBoard(ctx, board, coll); processErr != nil {
			golog.Error("Failed to process board", "board", board, "error", processErr)
			boardStatistics[board].ErrorCount++
			// Continue with other boards instead of failing completely
		}
	}

	// Send statistics email if configured
	if len(emails) > 0 {
		if emailErr := sendStatisticsEmail(emails); emailErr != nil {
			golog.Error("Failed to send statistics email", "error", emailErr)
			// Don't fail the entire process for email errors
		} else {
			golog.Info("Statistics email sent successfully", "recipients", emails)
		}
	}

	return nil
}

// processSingleBoard processes a single board
func processSingleBoard(ctx context.Context, board string, coll *gomongo.MongoCollection) error {

	// Build query based on whether specific IDs or custom query are provided
	var query bson.M
	if *idFlag != "" {
		// Parse IDs - expected format: "[id1,id2,id3]" (also supports legacy comma-separated format for compatibility)
		idString := strings.TrimSpace(*idFlag)

		// Remove brackets if present
		if strings.HasPrefix(idString, "[") && strings.HasSuffix(idString, "]") {
			idString = strings.TrimPrefix(idString, "[")
			idString = strings.TrimSuffix(idString, "]")
		}

		// Split by comma and trim whitespace
		idList := strings.Split(idString, ",")
		for i, id := range idList {
			idList[i] = strings.TrimSpace(id)
		}

		// Remove empty strings
		var cleanIdList []string
		for _, id := range idList {
			if id != "" {
				cleanIdList = append(cleanIdList, id)
			}
		}

		if len(cleanIdList) == 1 {
			// Single ID
			query = bson.M{"_id": cleanIdList[0]}
			golog.Info("Processing specific ID", "id", cleanIdList[0], "board", board)
		} else {
			// Multiple IDs
			query = bson.M{"_id": bson.M{"$in": cleanIdList}}
			golog.Info("Processing specific IDs", "ids", cleanIdList, "count", len(cleanIdList), "board", board)
		}
	} else if *queryFlag != "" {
		// Parse custom query from JSON (support both standard JSON and simplified format)
		queryStr := preprocessQuery(*queryFlag)
		err := bson.UnmarshalExtJSON([]byte(queryStr), true, &query)
		if err != nil {
			return fmt.Errorf("failed to parse custom query JSON: %v", err)
		}
		golog.Info("Processing with custom query", "query", query, "board", board)
	} else {
		// If no ID or custom query provided, query documents without phoLH field or with empty phoLH
		query = bson.M{
			"$or": []bson.M{
				{"phoLH": bson.M{"$eq": nil}},
				{"phoLH": bson.M{"$size": 0}}, // empty array
			},
		}
		golog.Info("Processing documents without phoLH", "board", board)
	}

	// Add time range filter if specified
	if *monthsFlag > 0 {
		timeLimit := time.Now().AddDate(0, -*monthsFlag, 0)
		if query == nil {
			query = bson.M{}
		}
		query["ts"] = bson.M{"$gte": timeLimit}
		golog.Info("Added time range filter", "months", *monthsFlag, "timeLimit", timeLimit, "board", board)
	}

	// Get cursor
	golog.Info("Executing query", "query", query, "board", board)
	cursor, err := coll.Find(ctx, query)
	if err != nil {
		golog.Error("Failed to execute query", "error", err, "board", board)
		return fmt.Errorf("failed to execute query for board %s: %v", board, err)
	}
	golog.Info("Query executed successfully, starting streaming", "board", board)

	opts := gostreaming.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			return processItem(ctx, item, board)
		},
		End: func(err error) {
			duration := time.Since(startTime)
			golog.Info("Board processing completed", "board", board, "duration", duration)
			// Check if error is not nil and has actual content
			if err != nil && err.Error() != "" {
				golog.Error("Stream ended with error", "board", board, "error", err, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed successfully", "board", board, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
		},
		Error: func(err error) {
			golog.Error("Processing error", "board", board, "error", err)
			boardStatistics[board].ErrorCount++
		},
		High:    10,
		Verbose: 2,
	}

	golog.Info("Starting gostreaming.Streaming", "board", board)
	err = gostreaming.Streaming(ctx, &opts)
	if err != nil {
		golog.Error("Failed to stream data", "board", board, "error", err)
		return fmt.Errorf("failed to stream data for board %s: %v", board, err)
	}
	golog.Info("Streaming completed successfully", "board", board)
	return nil
}

// processItem processes a single document to add to download queue
func processItem(ctx context.Context, item interface{}, board string) error {
	_ = ctx // Context not used in this function but required by gostreaming interface
	golog.Debug("Processing item started", "board", board)

	// Track processing speed
	speedMeter.Check("processed", 1)
	boardStatistics[board].ProcessedCount++

	// Convert item to bson.M (handle both bson.M and bson.D types)
	var doc bson.M
	switch v := item.(type) {
	case bson.M:
		doc = v
	case bson.D:
		// Convert bson.D to bson.M using marshal/unmarshal
		data, err := bson.Marshal(v)
		if err != nil {
			golog.Error("Failed to marshal bson.D", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to marshal bson.D: %v", err)
		}
		if err := bson.Unmarshal(data, &doc); err != nil {
			golog.Error("Failed to unmarshal to bson.M", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to unmarshal to bson.M: %v", err)
		}
	default:
		golog.Error("Unsupported document type", "type", fmt.Sprintf("%T", item), "item", item, "board", board)
		speedMeter.Check("errors", 1)
		boardStatistics[board].ErrorCount++
		return fmt.Errorf("unsupported document type: %T", item)
	}

	golog.Debug("Item converted to bson.M successfully", "board", board)

	// Extract required fields
	id, ok := doc["_id"].(string)
	if !ok {
		golog.Error("Failed to extract _id", "doc", doc, "board", board)
		speedMeter.Check("idErrors", 1)
		boardStatistics[board].ErrorCount++
		return fmt.Errorf("failed to extract _id")
	}

	// Check for media field without phoLH (new requirement)
	hasMedia := hasMediaField(doc, board)
	hasPhoLH := hasPhoLHField(doc)

	if hasMedia && !hasPhoLH {
		// Check if already exists in queue
		exists, err := checkQueueExists(id, board)
		if err != nil {
			golog.Error("Failed to check queue existence", "_id", id, "board", board, "error", err)
			speedMeter.Check("queueCheckErrors", 1)
			boardStatistics[board].ErrorCount++
			return fmt.Errorf("failed to check queue existence: %w", err)
		}

		boardStatistics[board].MediaCheckCount++

		if exists {
			golog.Debug("Skipping document: already exists in queue", "_id", id, "board", board)
			speedMeter.Check("skipped_exists_in_queue", 1)
			boardStatistics[board].SkippedCount++
			return nil
		}

		// Document has media but no phoLH and not in queue - add it
		golog.Debug("Document has media field but no phoLH, adding to queue", "_id", id, "board", board)
		boardStatistics[board].MediaAddedCount++
	} else {
		// If processing specific IDs (idFlag provided) or custom query (queryFlag provided), skip phoLH check and process directly
		// Otherwise, check if document has phoLH - if yes, skip it
		if *idFlag == "" && *queryFlag == "" {
			if hasPhoLH {
				golog.Debug("Skipping document: has phoLH", "_id", id, "board", board)
				speedMeter.Check("skipped_has_phoLH", 1)
				boardStatistics[board].SkippedCount++
				return nil
			}
		}
		// For specific ID processing or custom query, no phoLH check needed - process directly
	}

	golog.Debug("Document needs to be added to queue", "_id", id, "board", board)

	// Get priority for queue
	priority := getPriority(doc, board)
	if priority < 0 {
		golog.Error("invalid priority value", "priority", priority, "propId", id, "board", board)
		speedMeter.Check("priorityErrors", 1)
		boardStatistics[board].ErrorCount++
		return fmt.Errorf("invalid priority value: %d", priority)
	}

	// Check if this is a dry run
	if *dryrunFlag {
		speedMeter.Check("dryrun", 1)
		boardStatistics[board].AddedCount++
		golog.Info("Dry run mode: Would add to queue",
			"_id", id,
			"board", board,
			"priority", priority)
		return nil
	}

	// Add to download queue
	err := queue.AddToQueue(id, priority, board)
	if err != nil {
		golog.Error("Failed to add to queue",
			"_id", id,
			"board", board,
			"error", err)
		speedMeter.Check("queueErrors", 1)
		boardStatistics[board].ErrorCount++
		return fmt.Errorf("failed to add to queue: %w", err)
	}

	// Track successful additions
	speedMeter.Check("added", 1)
	boardStatistics[board].AddedCount++

	golog.Info("Successfully added to queue",
		"_id", id,
		"board", board,
		"priority", priority)

	return nil
}

// getPriority calculates priority for a document
func getPriority(doc bson.M, board string) int {
	// If processing specific IDs or custom query (from command line), always use priority 50000
	if *idFlag != "" || *queryFlag != "" {
		return 50000
	}

	// Use the goresodownload package's priority calculator for batch processing
	priority, err := goresodownload.CalculatePriority(board, doc)
	if err != nil {
		golog.Debug("Failed to calculate priority, using default", "error", err, "board", board)
		return 1000 // Default priority for batch processing
	}
	return priority
}

func main() {
	// Create context without timeout to allow long-running operations
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	golog.Info("Starting AddToQueue batch process")
	if err := processProperties(ctx); err != nil {
		golog.Fatal("Failed to process properties", "error", err)
	}
	golog.Info("AddToQueue batch process completed successfully", "boardStatistics", boardStatistics)
}
